extends Control

# Grid constants
const GRID_SIZE = 9
const CELL_SIZE = 50

# UI
var grid_offset_x
var grid_offset_y

# Pause menu
var is_paused = false
var menu_buttons = ["Continue", "Songs", "Quit"]
var selected_button = 0
var button_rects = []
var in_songs_menu = false

# Victory screen
var victory_screen = false
var victory_buttons = ["Restart", "New Song", "Exit"]

# References
var rhythm_manager: Node
var song_manager: Node
var player: Node

func _ready():
	# Get references
	rhythm_manager = get_node("../RhythmManager")
	song_manager = get_node("../SongManager")
	player = get_node("../Player")
	
	# Calculate grid offset
	var viewport_size = get_viewport().get_visible_rect().size
	grid_offset_x = (viewport_size.x - GRID_SIZE * CELL_SIZE) / 2
	grid_offset_y = (viewport_size.y - GRID_SIZE * CELL_SIZE) / 2

func _draw():
	draw_grid()
	draw_player()
	draw_beat_indicator()
	draw_ui()
	
	if victory_screen:
		draw_victory_screen()
	elif is_paused:
		if in_songs_menu:
			draw_songs_menu()
		else:
			draw_pause_menu()

func draw_grid():
	var grid_color = Color.GRAY
	for x in range(GRID_SIZE + 1):
		var start_x = grid_offset_x + x * CELL_SIZE
		draw_line(Vector2(start_x, grid_offset_y), Vector2(start_x, grid_offset_y + GRID_SIZE * CELL_SIZE), grid_color, 1)
	for y in range(GRID_SIZE + 1):
		var start_y = grid_offset_y + y * CELL_SIZE
		draw_line(Vector2(grid_offset_x, start_y), Vector2(grid_offset_x + GRID_SIZE * CELL_SIZE, start_y), grid_color, 1)

func draw_player():
	var player_pos = player.get_player_position()
	var screen_pos = Vector2(
		grid_offset_x + player_pos.x * CELL_SIZE + CELL_SIZE / 2,
		grid_offset_y + player_pos.y * CELL_SIZE + CELL_SIZE / 2
	)
	draw_circle(screen_pos, 20, Color.BLUE)

func draw_beat_indicator():
	var progress = rhythm_manager.get_beat_progress_for_frame(rhythm_manager.current_frame)
	var radius = 20 + int(10 * (1 - progress))
	var color = Color.RED if progress > 0.8 else Color.YELLOW
	draw_circle(Vector2(50, 50), radius, color)

func draw_ui():
	var font = ThemeDB.fallback_font
	var viewport_size = get_viewport().get_visible_rect().size
	
	# BPM and frame info
	var current_bpm = rhythm_manager.get_current_bpm_for_frame(rhythm_manager.current_frame)
	draw_string(font, Vector2(10, 100), "BPM: " + str(current_bpm), HORIZONTAL_ALIGNMENT_LEFT, -1, 24, Color.WHITE)
	draw_string(font, Vector2(10, 125), "Frame: " + str(rhythm_manager.current_frame), HORIZONTAL_ALIGNMENT_LEFT, -1, 18, Color.GRAY)
	
	# Instructions
	var instructions = ["Arrow keys: Move on beat", "ESC: Pause"]
	for i in range(instructions.size()):
		draw_string(font, Vector2(10, 160 + i * 25), instructions[i], HORIZONTAL_ALIGNMENT_LEFT, -1, 18, Color.WHITE)
	
	# Hit quality
	if player.get_hit_display_time() > 0:
		var last_hit_quality = player.get_last_hit_quality()
		var quality_text = ""
		var quality_color = Color.WHITE
		match last_hit_quality:
			4: # EXACT
				quality_text = "EXACT"
				quality_color = Color.MAGENTA
			3: # PERFECT
				quality_text = "PERFECT"
				quality_color = Color.GOLD
			2: # EXCELLENT
				quality_text = "EXCELLENT"
				quality_color = Color.CYAN
			1: # GOOD
				quality_text = "GOOD"
				quality_color = Color.GREEN
			0: # MISS
				quality_text = "MISS"
				quality_color = Color.RED
		
		var text_size = font.get_string_size(quality_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 32)
		var pos = Vector2(viewport_size.x / 2 - text_size.x / 2, viewport_size.y - 50)
		draw_string(font, pos, quality_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 32, quality_color)
	
	# Combos
	var combo_text = "Combo: " + str(player.get_total_combo())
	var combo_size = font.get_string_size(combo_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 24)
	draw_string(font, Vector2(viewport_size.x - combo_size.x - 10, 30), combo_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 24, Color.WHITE)
	
	if player.get_perfect_combo() > 0:
		var perfect_text = "Perfect: " + str(player.get_perfect_combo())
		var perfect_size = font.get_string_size(perfect_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 18)
		draw_string(font, Vector2(viewport_size.x - perfect_size.x - 10, 55), perfect_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 18, Color.GOLD)

func toggle_pause():
	is_paused = !is_paused
	if is_paused:
		selected_button = 0  # Reset to first button

func open_songs_menu():
	in_songs_menu = true
	selected_button = song_manager.get_current_song_index()

func is_game_paused():
	return is_paused

func get_selected_button():
	return selected_button

func set_selected_button(value):
	selected_button = value

func is_in_songs_menu():
	return in_songs_menu

func set_in_songs_menu(value):
	in_songs_menu = value

func get_button_rects():
	return button_rects

func show_victory_screen():
	victory_screen = true
	selected_button = 0  # Reset to first button

func hide_victory_screen():
	victory_screen = false

func is_victory_screen():
	return victory_screen

func draw_pause_menu():
	var font = ThemeDB.fallback_font
	var viewport_size = get_viewport().get_visible_rect().size

	# Semi-transparent overlay
	draw_rect(Rect2(Vector2.ZERO, viewport_size), Color(0, 0, 0, 0.7))

	# Menu background color (blue-purple with more blue)
	var menu_bg_color = Color(0.3, 0.4, 0.8, 0.9)  # More blue than purple
	var menu_width = 300
	var menu_height = 250
	var menu_pos = Vector2(viewport_size.x / 2 - menu_width / 2, viewport_size.y / 2 - menu_height / 2)
	draw_rect(Rect2(menu_pos, Vector2(menu_width, menu_height)), menu_bg_color)

	# "PAUSED" title
	var title_text = "PAUSED"
	var title_size = font.get_string_size(title_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 48)
	var title_pos = Vector2(viewport_size.x / 2 - title_size.x / 2, menu_pos.y + 50)
	draw_string(font, title_pos, title_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 48, Color.WHITE)

	# Menu buttons
	button_rects.clear()
	var button_start_y = menu_pos.y + 100
	var button_height = 40
	var button_spacing = 10

	for i in range(menu_buttons.size()):
		var button_y = button_start_y + i * (button_height + button_spacing)
		var button_rect = Rect2(menu_pos.x + 50, button_y, menu_width - 100, button_height)
		button_rects.append(button_rect)

		# Button colors
		var button_color = Color(0.4, 0.5, 0.9, 0.8)  # Slightly more blue
		var highlight_color = Color(0.6, 0.8, 1.0, 0.9)  # Baby blue

		if i == selected_button:
			draw_rect(button_rect, highlight_color)
		else:
			draw_rect(button_rect, button_color)

		# Button text
		var button_text = menu_buttons[i]
		var text_size = font.get_string_size(button_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 24)
		var text_pos = Vector2(
			button_rect.position.x + button_rect.size.x / 2 - text_size.x / 2,
			button_rect.position.y + button_rect.size.y / 2 + text_size.y / 2
		)
		draw_string(font, text_pos, button_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 24, Color.WHITE)

func draw_songs_menu():
	var font = ThemeDB.fallback_font
	var viewport_size = get_viewport().get_visible_rect().size

	# Semi-transparent overlay
	draw_rect(Rect2(Vector2.ZERO, viewport_size), Color(0, 0, 0, 0.7))

	# Menu background color (blue-purple with more blue)
	var menu_bg_color = Color(0.3, 0.4, 0.8, 0.9)
	var menu_width = 400
	var menu_height = 350
	var menu_pos = Vector2(viewport_size.x / 2 - menu_width / 2, viewport_size.y / 2 - menu_height / 2)
	draw_rect(Rect2(menu_pos, Vector2(menu_width, menu_height)), menu_bg_color)

	# "SONGS" title
	var title_text = "SONGS"
	var title_size = font.get_string_size(title_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 48)
	var title_pos = Vector2(viewport_size.x / 2 - title_size.x / 2, menu_pos.y + 50)
	draw_string(font, title_pos, title_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 48, Color.WHITE)

	# Song list
	button_rects.clear()
	var button_start_y = menu_pos.y + 100
	var button_height = 35
	var button_spacing = 5
	var available_songs = song_manager.get_available_songs()

	for i in range(available_songs.size()):
		var button_y = button_start_y + i * (button_height + button_spacing)
		var button_rect = Rect2(menu_pos.x + 30, button_y, menu_width - 60, button_height)
		button_rects.append(button_rect)

		# Button colors
		var button_color = Color(0.4, 0.5, 0.9, 0.8)  # Slightly more blue
		var highlight_color = Color(0.6, 0.8, 1.0, 0.9)  # Baby blue

		if i == selected_button:
			draw_rect(button_rect, highlight_color)
		else:
			draw_rect(button_rect, button_color)

		# Song name
		var song_name = available_songs[i]
		var text_size = font.get_string_size(song_name, HORIZONTAL_ALIGNMENT_CENTER, -1, 20)
		var text_pos = Vector2(
			button_rect.position.x + button_rect.size.x / 2 - text_size.x / 2,
			button_rect.position.y + button_rect.size.y / 2 + text_size.y / 2
		)
		draw_string(font, text_pos, song_name, HORIZONTAL_ALIGNMENT_LEFT, -1, 20, Color.WHITE)

	# Instructions
	var instruction_text = "ESC: Back    ENTER: Select Song"
	var instruction_size = font.get_string_size(instruction_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 16)
	var instruction_pos = Vector2(viewport_size.x / 2 - instruction_size.x / 2, menu_pos.y + menu_height - 30)
	draw_string(font, instruction_pos, instruction_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 16, Color.LIGHT_GRAY)

func draw_victory_screen():
	var font = ThemeDB.fallback_font
	var viewport_size = get_viewport().get_visible_rect().size

	# Semi-transparent overlay
	draw_rect(Rect2(Vector2.ZERO, viewport_size), Color(0, 0, 0, 0.8))

	# Menu background color (blue-purple with more blue)
	var menu_bg_color = Color(0.3, 0.4, 0.8, 0.9)
	var menu_width = 300
	var menu_height = 250
	var menu_pos = Vector2(viewport_size.x / 2 - menu_width / 2, viewport_size.y / 2 - menu_height / 2)
	draw_rect(Rect2(menu_pos, Vector2(menu_width, menu_height)), menu_bg_color)

	# "VICTORY" title
	var title_text = "VICTORY"
	var title_size = font.get_string_size(title_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 48)
	var title_pos = Vector2(viewport_size.x / 2 - title_size.x / 2, menu_pos.y + 50)
	draw_string(font, title_pos, title_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 48, Color.GOLD)

	# Victory buttons
	button_rects.clear()
	var button_start_y = menu_pos.y + 100
	var button_height = 40
	var button_spacing = 10

	for i in range(victory_buttons.size()):
		var button_y = button_start_y + i * (button_height + button_spacing)
		var button_rect = Rect2(menu_pos.x + 50, button_y, menu_width - 100, button_height)
		button_rects.append(button_rect)

		# Button colors
		var button_color = Color(0.4, 0.5, 0.9, 0.8)  # Slightly more blue
		var highlight_color = Color(0.6, 0.8, 1.0, 0.9)  # Baby blue

		if i == selected_button:
			draw_rect(button_rect, highlight_color)
		else:
			draw_rect(button_rect, button_color)

		# Button text
		var button_text = victory_buttons[i]
		var text_size = font.get_string_size(button_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 24)
		var text_pos = Vector2(
			button_rect.position.x + button_rect.size.x / 2 - text_size.x / 2,
			button_rect.position.y + button_rect.size.y / 2 + text_size.y / 2
		)
		draw_string(font, text_pos, button_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 24, Color.WHITE)
