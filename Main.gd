extends Node2D

# Constants
const GRID_SIZE = 8
const CELL_SIZE = 60

# Game state
var player_pos = Vector2(4, 4)
var can_move = true

# Frame-based rhythm system (completely different approach)
var bpm = 120
var frames_per_beat = 72  # At 120fps: 120fps / (120bpm/60) = 60 frames per beat
var game_start_frame = 0
var current_frame = 0

# Hit quality lookup table (pre-calculated)
var frame_quality = {}     # frame_number: quality_value (0-4)

# Game state
var last_hit_quality = 0  # 0-4 numerical value
var hit_display_time = 0.0
var total_combo = 0
var perfect_combo = 0
var frames_since_last_move = 0

# Audio
var audio_player: AudioStreamPlayer
var last_click_frame = -1000

# UI
var grid_offset_x
var grid_offset_y

# Pause menu
var is_paused = false
var menu_buttons = ["Continue", "Songs", "Quit"]
var selected_button = 0
var button_rects = []

# Songs system
var available_songs = []
var current_song_index = 0
var in_songs_menu = false

# Variable BPM system
var variable_beats = false
var bpm_changes = []  # Array of {start_frame: int, end_frame: int, bpm: int}

func _ready():
	# Calculate grid offset
	grid_offset_x = (get_viewport().get_visible_rect().size.x - GRID_SIZE * CELL_SIZE) / 2
	grid_offset_y = (get_viewport().get_visible_rect().size.y - GRID_SIZE * CELL_SIZE) / 2

	# Set up audio
	setup_audio()

	# Load available songs
	load_songs()

	# Initialize frame-based rhythm system with default song
	load_song_by_index(0)  # Default to first song (120 BPM)
	game_start_frame = Engine.get_process_frames()

func setup_audio():
	audio_player = AudioStreamPlayer.new()
	add_child(audio_player)
	var stream = AudioStreamGenerator.new()
	stream.mix_rate = 22050.0
	stream.buffer_length = 0.1
	audio_player.stream = stream
	audio_player.volume_db = -5

func set_bpm(new_bpm):
	bpm = new_bpm
	frames_per_beat = int(120.0 / (new_bpm / 60.0))  # 120fps assumption
	generate_hit_quality_tables()

func generate_hit_quality_tables():
	# Clear existing table
	frame_quality.clear()

	if variable_beats and bpm_changes.size() > 0:
		# Generate based on variable BPM changes
		generate_variable_beat_mapping()
	else:
		# Generate standard mapping for constant BPM
		generate_standard_beat_mapping()

func generate_standard_beat_mapping():
	# Generate for next 100000 frames (about 14 minutes at 120fps)
	for frame in range(100000):
		var frame_in_beat = frame % frames_per_beat
		var distance_from_beat_start = min(frame_in_beat, frames_per_beat - frame_in_beat)

		# Define frame windows with numerical values
		# 0=MISS, 1=GOOD, 2=EXCELLENT, 3=PERFECT, 4=EXACT
		if distance_from_beat_start == 0:
			frame_quality[frame] = 4  # EXACT
		elif distance_from_beat_start <= 4:  # ±4 frames for perfect
			frame_quality[frame] = 3  # PERFECT
		elif distance_from_beat_start <= 8: # ±8 frames for excellent
			frame_quality[frame] = 2  # EXCELLENT
		elif distance_from_beat_start <= 15: # ±15 frames for good
			frame_quality[frame] = 1  # GOOD
		else:
			frame_quality[frame] = 0  # MISS

func generate_variable_beat_mapping():
	# Generate mapping based on BPM changes
	for bpm_change in bpm_changes:
		var start_frame = bpm_change.start_frame
		var end_frame = bpm_change.end_frame
		var section_bpm = bpm_change.bpm
		var section_frames_per_beat = int(120.0 / (section_bpm / 60.0))

		for frame in range(start_frame, end_frame + 1):
			var frame_in_beat = frame % section_frames_per_beat
			var distance_from_beat_start = min(frame_in_beat, section_frames_per_beat - frame_in_beat)

			# Define frame windows with numerical values
			if distance_from_beat_start == 0:
				frame_quality[frame] = 4  # EXACT
			elif distance_from_beat_start <= 5:  # ±5 frames for perfect
				frame_quality[frame] = 3  # PERFECT
			elif distance_from_beat_start <= 12: # ±12 frames for excellent
				frame_quality[frame] = 2  # EXCELLENT
			elif distance_from_beat_start <= 24: # ±24 frames for good
				frame_quality[frame] = 1  # GOOD
			else:
				frame_quality[frame] = 0  # MISS

func get_current_frame():
	return Engine.get_process_frames() - game_start_frame

func get_hit_quality_for_frame(frame_number):
	if frame_number in frame_quality:
		return frame_quality[frame_number]
	else:
		return 0  # MISS if not found

func should_play_metronome_at_frame(frame_number):
	if variable_beats and bpm_changes.size() > 0:
		# Find which BPM section this frame is in
		for bpm_change in bpm_changes:
			if frame_number >= bpm_change.start_frame and frame_number <= bpm_change.end_frame:
				var section_bpm = bpm_change.bpm
				var section_frames_per_beat = int(120.0 / (section_bpm / 60.0))
				var frame_in_beat = frame_number % section_frames_per_beat
				return frame_in_beat == 0
		return false
	else:
		# Standard constant BPM
		var frame_in_beat = frame_number % frames_per_beat
		return frame_in_beat == 0

func get_frames_per_beat_for_frame(frame_number):
	if variable_beats and bpm_changes.size() > 0:
		# Find which BPM section this frame is in
		for bpm_change in bpm_changes:
			if frame_number >= bpm_change.start_frame and frame_number <= bpm_change.end_frame:
				var section_bpm = bpm_change.bpm
				return int(120.0 / (section_bpm / 60.0))
		return frames_per_beat  # Fallback
	else:
		return frames_per_beat

func get_current_bpm_for_frame(frame_number):
	if variable_beats and bpm_changes.size() > 0:
		# Find which BPM section this frame is in
		for bpm_change in bpm_changes:
			if frame_number >= bpm_change.start_frame and frame_number <= bpm_change.end_frame:
				return bpm_change.bpm
		return bpm  # Fallback
	else:
		return bpm

func get_beat_progress_for_frame(frame_number):
	var current_frames_per_beat = get_frames_per_beat_for_frame(frame_number)
	var frame_in_beat = frame_number % current_frames_per_beat
	return float(frame_in_beat) / float(current_frames_per_beat)

func _process(delta):
	# Handle pause menu input first
	if Input.is_action_just_pressed("ui_cancel"):  # Escape key
		toggle_pause()

	if is_paused:
		handle_menu_input()
		queue_redraw()
		return

	current_frame = get_current_frame()
	frames_since_last_move += 1

	# Check if we're in miss territory and reset can_move
	var hit_quality = get_hit_quality_for_frame(current_frame)
	if hit_quality == 0:  # MISS
		can_move = true

	# Handle input
	handle_movement_input()

	# Check for beat (for metronome only)
	if should_play_metronome_at_frame(current_frame) and current_frame != last_click_frame:
		last_click_frame = current_frame
		play_metronome_click()

	# Reset combo if standing still too long (about 1.5 beats)
	if frames_since_last_move > frames_per_beat * 1.5:
		reset_combos()



	# Update UI timers
	if hit_display_time > 0:
		hit_display_time -= delta

	queue_redraw()

func handle_movement_input():
	var move_vector = Vector2.ZERO

	if Input.is_action_just_pressed("move_up"):
		move_vector.y = -1
	elif Input.is_action_just_pressed("move_down"):
		move_vector.y = 1
	elif Input.is_action_just_pressed("move_left"):
		move_vector.x = -1
	elif Input.is_action_just_pressed("move_right"):
		move_vector.x = 1

	if move_vector != Vector2.ZERO:
		# Get hit quality for THIS exact frame
		var hit_quality = get_hit_quality_for_frame(current_frame)

		if hit_quality > 0:  # Any quality above MISS
			try_move(move_vector, hit_quality)
		else:
			# Miss - show feedback and reset combos
			reset_combos()
			last_hit_quality = 0  # MISS
			hit_display_time = 2.0

func try_move(direction, quality):
	if not can_move:
		reset_combos()
		last_hit_quality = 0  # MISS
		hit_display_time = 2.0
		return
	
	var new_pos = player_pos + direction
	if new_pos.x >= 0 and new_pos.x < GRID_SIZE and new_pos.y >= 0 and new_pos.y < GRID_SIZE:
		# Successful move
		player_pos = new_pos
		can_move = false
		frames_since_last_move = 0
		
		# Update hit quality display
		last_hit_quality = quality
		hit_display_time = 2.0
		
		# Update combos
		total_combo += 1
		if quality >= 3:  # PERFECT (3) or EXACT (4)
			perfect_combo += 1
		else:
			perfect_combo = 0

func reset_combos():
	total_combo = 0
	perfect_combo = 0

func toggle_pause():
	is_paused = !is_paused
	if is_paused:
		selected_button = 0  # Reset to first button

func handle_menu_input():
	if in_songs_menu:
		handle_songs_menu_input()
	else:
		handle_pause_menu_input()

func handle_pause_menu_input():
	# Keyboard navigation
	if Input.is_action_just_pressed("move_up"):
		selected_button = (selected_button - 1) % menu_buttons.size()
	elif Input.is_action_just_pressed("move_down"):
		selected_button = (selected_button + 1) % menu_buttons.size()
	elif Input.is_action_just_pressed("ui_accept"):  # Enter key
		execute_menu_action()

	# Mouse navigation
	var mouse_pos = get_global_mouse_position()
	for i in range(button_rects.size()):
		if button_rects[i].has_point(mouse_pos):
			selected_button = i
			if Input.is_action_just_pressed("ui_click"):
				execute_menu_action()

func handle_songs_menu_input():
	# Keyboard navigation
	if Input.is_action_just_pressed("move_up"):
		selected_button = (selected_button - 1) % available_songs.size()
	elif Input.is_action_just_pressed("move_down"):
		selected_button = (selected_button + 1) % available_songs.size()
	elif Input.is_action_just_pressed("ui_accept"):  # Enter key
		load_song_by_index(selected_button)
		in_songs_menu = false
		toggle_pause()  # Close pause menu after selecting song
	elif Input.is_action_just_pressed("ui_cancel"):  # Escape to go back
		in_songs_menu = false
		selected_button = 1  # Back to "Songs" button

	# Mouse navigation
	var mouse_pos = get_global_mouse_position()
	for i in range(button_rects.size()):
		if button_rects[i].has_point(mouse_pos):
			selected_button = i
			if Input.is_action_just_pressed("ui_click"):
				load_song_by_index(selected_button)
				in_songs_menu = false
				toggle_pause()

func execute_menu_action():
	match selected_button:
		0:  # Continue
			toggle_pause()
		1:  # Songs
			open_songs_menu()
		2:  # Quit
			get_tree().quit()

func load_songs():
	available_songs.clear()
	var song_data = []

	var dir = DirAccess.open("res://songs/")
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			if file_name.ends_with(".txt"):
				var file_path = "res://songs/" + file_name
				var file = FileAccess.open(file_path, FileAccess.READ)
				if file:
					var first_line = file.get_line()
					file.close()

					# Parse display name from first line (e.g., "1. 120 BPM")
					var display_name = first_line.strip_edges()
					var file_base = file_name.get_basename()

					# Extract ID number for sorting
					var id = 999  # Default high number if no ID found
					if display_name.begins_with(str(display_name.split(".")[0])):
						id = int(display_name.split(".")[0])

					song_data.append({"id": id, "display_name": display_name, "file_name": file_base})
			file_name = dir.get_next()
		dir.list_dir_end()

	# Sort by ID number
	song_data.sort_custom(func(a, b): return a.id < b.id)

	# Extract display names in sorted order
	for song in song_data:
		available_songs.append(song.display_name)

func load_song_by_index(index):
	if index >= 0 and index < available_songs.size():
		current_song_index = index
		var song_name = available_songs[index]
		load_song(song_name)

func load_song(song_display_name):
	# Find the corresponding file for this display name
	var dir = DirAccess.open("res://songs/")
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			if file_name.ends_with(".txt"):
				var file_path = "res://songs/" + file_name
				var file = FileAccess.open(file_path, FileAccess.READ)
				if file:
					var first_line = file.get_line()
					if first_line.strip_edges() == song_display_name:
						# Found the right file, parse the rest
						var content = file.get_as_text()
						file.close()

						# Reset variable beats system
						variable_beats = false
						bpm_changes.clear()

						# Parse song file
						var default_bpm = 120
						var lines = content.split("\n")
						for line in lines:
							line = line.strip_edges()
							if line.begins_with("bpm="):
								default_bpm = int(line.split("=")[1])
							elif line.begins_with("variableBeats="):
								variable_beats = line.split("=")[1].to_lower() == "true"
							elif line.begins_with("bpm_change:"):
								# Format: bpm_change:start_frame:end_frame:bpm
								var parts = line.split(":")
								if parts.size() == 4:
									var change = {
										"start_frame": int(parts[1]),
										"end_frame": int(parts[2]),
										"bpm": int(parts[3])
									}
									bpm_changes.append(change)

						# Only set BPM if not using variable beats
						if not variable_beats:
							set_bpm(default_bpm)
						else:
							bpm = default_bpm  # Store for display purposes only
							generate_hit_quality_tables()  # Generate variable beat mapping

						dir.list_dir_end()
						break
					file.close()
			file_name = dir.get_next()
		dir.list_dir_end()

		# Reset player position and frame count when loading new song
		player_pos = Vector2(4, 4)
		can_move = true
		reset_combos()
		frames_since_last_move = 0
		game_start_frame = Engine.get_process_frames()  # Reset frame count

		print("Loaded song: " + song_display_name + " (BPM: " + str(bpm) + ")")

func open_songs_menu():
	in_songs_menu = true
	selected_button = current_song_index

func play_metronome_click():
	if audio_player and audio_player.stream:
		var playback = audio_player.get_stream_playback()
		if playback:
			var click_length = 0.05
			var sample_rate = 22050.0
			var frequency = 800.0
			var samples = int(sample_rate * click_length)
			
			for i in range(samples):
				var t = float(i) / sample_rate
				var amplitude = 0.3
				if i > samples * 0.7:
					amplitude *= (samples - i) / float(samples * 0.3)
				var sample_value = sin(2.0 * PI * frequency * t) * amplitude
				playback.push_frame(Vector2(sample_value, sample_value))
		
		if not audio_player.playing:
			audio_player.play()

func get_beat_progress():
	var frame_in_beat = current_frame % frames_per_beat
	return float(frame_in_beat) / float(frames_per_beat)

func _draw():
	draw_grid()
	draw_player()
	draw_beat_indicator()
	draw_ui()

	if is_paused:
		if in_songs_menu:
			draw_songs_menu()
		else:
			draw_pause_menu()

func draw_grid():
	var grid_color = Color.GRAY
	for x in range(GRID_SIZE + 1):
		var start_x = grid_offset_x + x * CELL_SIZE
		draw_line(Vector2(start_x, grid_offset_y), Vector2(start_x, grid_offset_y + GRID_SIZE * CELL_SIZE), grid_color, 1)
	for y in range(GRID_SIZE + 1):
		var start_y = grid_offset_y + y * CELL_SIZE
		draw_line(Vector2(grid_offset_x, start_y), Vector2(grid_offset_x + GRID_SIZE * CELL_SIZE, start_y), grid_color, 1)

func draw_player():
	var screen_pos = Vector2(
		grid_offset_x + player_pos.x * CELL_SIZE + CELL_SIZE / 2,
		grid_offset_y + player_pos.y * CELL_SIZE + CELL_SIZE / 2
	)
	draw_circle(screen_pos, 20, Color.BLUE)

func draw_beat_indicator():
	var progress = get_beat_progress_for_frame(current_frame)
	var radius = 20 + int(10 * (1 - progress))
	var color = Color.RED if progress > 0.8 else Color.YELLOW
	draw_circle(Vector2(50, 50), radius, color)

func draw_ui():
	var font = ThemeDB.fallback_font
	var viewport_size = get_viewport().get_visible_rect().size

	# BPM and frame info
	var current_bpm = get_current_bpm_for_frame(current_frame)
	draw_string(font, Vector2(10, 100), "BPM: " + str(current_bpm), HORIZONTAL_ALIGNMENT_LEFT, -1, 24, Color.WHITE)
	draw_string(font, Vector2(10, 125), "Frame: " + str(current_frame), HORIZONTAL_ALIGNMENT_LEFT, -1, 18, Color.GRAY)

	# Instructions
	var instructions = ["Arrow keys: Move on beat", "ESC: Pause"]
	for i in range(instructions.size()):
		draw_string(font, Vector2(10, 160 + i * 25), instructions[i], HORIZONTAL_ALIGNMENT_LEFT, -1, 18, Color.WHITE)

	# Hit quality
	if hit_display_time > 0:
		var quality_text = ""
		var quality_color = Color.WHITE
		match last_hit_quality:
			4: # EXACT
				quality_text = "EXACT"
				quality_color = Color.MAGENTA
			3: # PERFECT
				quality_text = "PERFECT"
				quality_color = Color.GOLD
			2: # EXCELLENT
				quality_text = "EXCELLENT"
				quality_color = Color.CYAN
			1: # GOOD
				quality_text = "GOOD"
				quality_color = Color.GREEN
			0: # MISS
				quality_text = "MISS"
				quality_color = Color.RED

		var text_size = font.get_string_size(quality_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 32)
		var pos = Vector2(viewport_size.x / 2 - text_size.x / 2, viewport_size.y - 50)
		draw_string(font, pos, quality_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 32, quality_color)

	# Combos
	var combo_text = "Combo: " + str(total_combo)
	var combo_size = font.get_string_size(combo_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 24)
	draw_string(font, Vector2(viewport_size.x - combo_size.x - 10, 30), combo_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 24, Color.WHITE)

	if perfect_combo > 0:
		var perfect_text = "Perfect: " + str(perfect_combo)
		var perfect_size = font.get_string_size(perfect_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 18)
		draw_string(font, Vector2(viewport_size.x - perfect_size.x - 10, 55), perfect_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 18, Color.GOLD)

func draw_pause_menu():
	var font = ThemeDB.fallback_font
	var viewport_size = get_viewport().get_visible_rect().size

	# Semi-transparent overlay
	draw_rect(Rect2(Vector2.ZERO, viewport_size), Color(0, 0, 0, 0.7))

	# Menu background color (blue-purple with more blue)
	var menu_bg_color = Color(0.3, 0.4, 0.8, 0.9)  # More blue than purple
	var menu_width = 300
	var menu_height = 250
	var menu_pos = Vector2(viewport_size.x / 2 - menu_width / 2, viewport_size.y / 2 - menu_height / 2)
	draw_rect(Rect2(menu_pos, Vector2(menu_width, menu_height)), menu_bg_color)

	# "PAUSED" title
	var title_text = "PAUSED"
	var title_size = font.get_string_size(title_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 48)
	var title_pos = Vector2(viewport_size.x / 2 - title_size.x / 2, menu_pos.y + 50)
	draw_string(font, title_pos, title_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 48, Color.WHITE)

	# Menu buttons
	button_rects.clear()
	var button_start_y = menu_pos.y + 100
	var button_height = 40
	var button_spacing = 10

	for i in range(menu_buttons.size()):
		var button_y = button_start_y + i * (button_height + button_spacing)
		var button_rect = Rect2(menu_pos.x + 50, button_y, menu_width - 100, button_height)
		button_rects.append(button_rect)

		# Button colors
		var button_color = Color(0.4, 0.5, 0.9, 0.8)  # Slightly more blue
		var highlight_color = Color(0.6, 0.8, 1.0, 0.9)  # Baby blue

		if i == selected_button:
			draw_rect(button_rect, highlight_color)
		else:
			draw_rect(button_rect, button_color)

		# Button text
		var button_text = menu_buttons[i]
		var text_size = font.get_string_size(button_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 24)
		var text_pos = Vector2(
			button_rect.position.x + button_rect.size.x / 2 - text_size.x / 2,
			button_rect.position.y + button_rect.size.y / 2 + text_size.y / 2
		)
		draw_string(font, text_pos, button_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 24, Color.WHITE)

func draw_songs_menu():
	var font = ThemeDB.fallback_font
	var viewport_size = get_viewport().get_visible_rect().size

	# Semi-transparent overlay
	draw_rect(Rect2(Vector2.ZERO, viewport_size), Color(0, 0, 0, 0.7))

	# Menu background color (blue-purple with more blue)
	var menu_bg_color = Color(0.3, 0.4, 0.8, 0.9)
	var menu_width = 400
	var menu_height = 350
	var menu_pos = Vector2(viewport_size.x / 2 - menu_width / 2, viewport_size.y / 2 - menu_height / 2)
	draw_rect(Rect2(menu_pos, Vector2(menu_width, menu_height)), menu_bg_color)

	# "SONGS" title
	var title_text = "SONGS"
	var title_size = font.get_string_size(title_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 48)
	var title_pos = Vector2(viewport_size.x / 2 - title_size.x / 2, menu_pos.y + 50)
	draw_string(font, title_pos, title_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 48, Color.WHITE)

	# Song list
	button_rects.clear()
	var button_start_y = menu_pos.y + 100
	var button_height = 35
	var button_spacing = 5

	for i in range(available_songs.size()):
		var button_y = button_start_y + i * (button_height + button_spacing)
		var button_rect = Rect2(menu_pos.x + 30, button_y, menu_width - 60, button_height)
		button_rects.append(button_rect)

		# Button colors
		var button_color = Color(0.4, 0.5, 0.9, 0.8)  # Slightly more blue
		var highlight_color = Color(0.6, 0.8, 1.0, 0.9)  # Baby blue

		if i == selected_button:
			draw_rect(button_rect, highlight_color)
		else:
			draw_rect(button_rect, button_color)

		# Song name
		var song_name = available_songs[i]
		var text_size = font.get_string_size(song_name, HORIZONTAL_ALIGNMENT_CENTER, -1, 20)
		var text_pos = Vector2(
			button_rect.position.x + button_rect.size.x / 2 - text_size.x / 2,
			button_rect.position.y + button_rect.size.y / 2 + text_size.y / 2
		)
		draw_string(font, text_pos, song_name, HORIZONTAL_ALIGNMENT_LEFT, -1, 20, Color.WHITE)

	# Instructions
	var instruction_text = "ESC: Back    ENTER: Test Here"
	var instruction_size = font.get_string_size(instruction_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 16)
	var instruction_pos = Vector2(viewport_size.x / 2 - instruction_size.x / 2, menu_pos.y + menu_height - 30)
	draw_string(font, instruction_pos, instruction_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 16, Color.LIGHT_GRAY)

func _input(event):
	# Mouse click handling for pause menu
	if is_paused and event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		var mouse_pos = event.position
		for i in range(button_rects.size()):
			if button_rects[i].has_point(mouse_pos):
				selected_button = i
				execute_menu_action()
				break
