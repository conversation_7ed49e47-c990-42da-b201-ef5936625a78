extends Node2D

# Player state
var player_pos = Vector2(4, 4)
var can_move = true
var frames_since_last_move = 0

# Game state
var last_hit_quality = 0  # 0-4 numerical value
var hit_display_time = 0.0
var total_combo = 0
var perfect_combo = 0

# Accuracy tracking
var hit_counts = {
	"exact": 0,      # 4 - 101% weight
	"perfect": 0,    # 3 - 100% weight
	"excellent": 0,  # 2 - 85% weight
	"good": 0,       # 1 - 70% weight
	"miss": 0        # 0 - 0% weight
}
var total_hits = 0

# References
var rhythm_manager: Node

func _ready():
	rhythm_manager = get_node("../RhythmManager")

func _process(delta):
	# Get reference to UI manager to check if paused
	var ui_manager = get_node("../UIManager")

	# Don't update timers or reset combos while paused
	if ui_manager and ui_manager.is_game_paused():
		return

	frames_since_last_move += 1

	# Reset combos if standing still too long (about 1.5 beats)
	var current_bpm = rhythm_manager.get_current_bpm_for_time(rhythm_manager.current_time)
	var beat_interval = 60.0 / current_bpm
	var time_since_last_move = frames_since_last_move * delta
	if time_since_last_move > beat_interval * 1.5:
		reset_combos()

	# Update UI timers
	if hit_display_time > 0:
		hit_display_time -= delta

func try_move(direction, quality):
	if not can_move:
		reset_combos()
		last_hit_quality = 0  # MISS
		hit_display_time = 2.0
		return
	
	# Move player
	var new_pos = player_pos + direction
	new_pos.x = clamp(new_pos.x, 0, 8)  # Assuming 9x9 grid (0-8)
	new_pos.y = clamp(new_pos.y, 0, 8)
	player_pos = new_pos
	
	# Update game state
	can_move = false
	frames_since_last_move = 0
	last_hit_quality = quality
	hit_display_time = 2.0
	
	# Update combos
	total_combo += 1
	if quality >= 3:  # PERFECT (3) or EXACT (4)
		perfect_combo += 1
	else:
		perfect_combo = 0

	# Update accuracy tracking
	update_accuracy(quality)

func reset_combos():
	total_combo = 0
	perfect_combo = 0

func update_accuracy(quality):
	total_hits += 1
	match quality:
		4: hit_counts["exact"] += 1
		3: hit_counts["perfect"] += 1
		2: hit_counts["excellent"] += 1
		1: hit_counts["good"] += 1
		0: hit_counts["miss"] += 1

func calculate_accuracy():
	if total_hits == 0:
		return 0.0

	# Weighted scoring: exact=101%, perfect=100%, excellent=85%, good=70%, miss=0%
	var weighted_score = (
		hit_counts["exact"] * 101.0 +
		hit_counts["perfect"] * 100.0 +
		hit_counts["excellent"] * 85.0 +
		hit_counts["good"] * 70.0 +
		hit_counts["miss"] * 0.0
	)

	return weighted_score / total_hits

func reset_accuracy():
	hit_counts = {
		"exact": 0,
		"perfect": 0,
		"excellent": 0,
		"good": 0,
		"miss": 0
	}
	total_hits = 0

func reset_position():
	player_pos = Vector2(4, 4)
	can_move = true
	frames_since_last_move = 0
	reset_accuracy()

func get_player_position():
	return player_pos

func get_last_hit_quality():
	return last_hit_quality

func get_hit_display_time():
	return hit_display_time

func get_total_combo():
	return total_combo

func get_perfect_combo():
	return perfect_combo

func set_can_move(value):
	can_move = value

func get_can_move():
	return can_move

func get_accuracy():
	return calculate_accuracy()

func get_total_hits():
	return total_hits
