extends Node2D

# Player state
var player_pos = Vector2(4, 4)
var can_move = true
var frames_since_last_move = 0

# Game state
var last_hit_quality = 0  # 0-4 numerical value
var hit_display_time = 0.0
var total_combo = 0
var perfect_combo = 0

# References
var rhythm_manager: Node

func _ready():
	rhythm_manager = get_node("../RhythmManager")

func _process(delta):
	frames_since_last_move += 1
	
	# Reset combos if standing still too long (about 1.5 beats)
	var current_frames_per_beat = rhythm_manager.get_frames_per_beat_for_frame(rhythm_manager.current_frame)
	if frames_since_last_move > current_frames_per_beat * 1.5:
		reset_combos()
	
	# Update UI timers
	if hit_display_time > 0:
		hit_display_time -= delta

func try_move(direction, quality):
	if not can_move:
		reset_combos()
		last_hit_quality = 0  # MISS
		hit_display_time = 2.0
		return
	
	# Move player
	var new_pos = player_pos + direction
	new_pos.x = clamp(new_pos.x, 0, 8)  # Assuming 9x9 grid (0-8)
	new_pos.y = clamp(new_pos.y, 0, 8)
	player_pos = new_pos
	
	# Update game state
	can_move = false
	frames_since_last_move = 0
	last_hit_quality = quality
	hit_display_time = 2.0
	
	# Update combos
	total_combo += 1
	if quality >= 3:  # PERFECT (3) or EXACT (4)
		perfect_combo += 1
	else:
		perfect_combo = 0

func reset_combos():
	total_combo = 0
	perfect_combo = 0

func reset_position():
	player_pos = Vector2(4, 4)
	can_move = true
	frames_since_last_move = 0

func get_player_position():
	return player_pos

func get_last_hit_quality():
	return last_hit_quality

func get_hit_display_time():
	return hit_display_time

func get_total_combo():
	return total_combo

func get_perfect_combo():
	return perfect_combo

func set_can_move(value):
	can_move = value

func get_can_move():
	return can_move
