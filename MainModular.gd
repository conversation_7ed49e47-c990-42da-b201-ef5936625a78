extends Node2D

# Child node references
var rhythm_manager: Node
var song_manager: Node
var player: Node
var ui_manager: Control

func _ready():
	# Create and add child nodes
	setup_child_nodes()

func setup_child_nodes():
	# Create RhythmManager
	rhythm_manager = Node.new()
	rhythm_manager.name = "RhythmManager"
	rhythm_manager.set_script(load("res://RhythmManager.gd"))
	add_child(rhythm_manager)
	
	# Create SongManager
	song_manager = Node.new()
	song_manager.name = "SongManager"
	song_manager.set_script(load("res://SongManager.gd"))
	add_child(song_manager)
	
	# Create Player
	player = Node2D.new()
	player.name = "Player"
	player.set_script(load("res://Player.gd"))
	add_child(player)
	
	# Create UIManager
	ui_manager = Control.new()
	ui_manager.name = "UIManager"
	ui_manager.set_script(load("res://UIManager.gd"))
	ui_manager.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)  # Fill the screen
	add_child(ui_manager)

func _process(delta):
	# Handle pause menu input first
	if Input.is_action_just_pressed("ui_cancel"):  # Escape key
		ui_manager.toggle_pause()
	
	if ui_manager.is_game_paused() or ui_manager.is_victory_screen():
		handle_menu_input()
		ui_manager.queue_redraw()
		return
	
	# Update current time
	rhythm_manager.current_time = rhythm_manager.get_current_time()

	# Check if we're in miss territory and reset can_move
	var hit_quality = rhythm_manager.get_hit_quality_for_time(rhythm_manager.current_time)
	if hit_quality == 0:  # MISS
		player.set_can_move(true)

	# Handle input
	handle_movement_input()

	# Check for beat (for metronome only)
	if rhythm_manager.should_play_metronome_at_time(rhythm_manager.current_time) and abs(rhythm_manager.current_time - rhythm_manager.last_click_time) > 0.1:
		rhythm_manager.last_click_time = rhythm_manager.current_time
		rhythm_manager.play_metronome_click()

	# Check for song completion (variable BPM songs only)
	if rhythm_manager.variable_beats and rhythm_manager.is_song_complete(rhythm_manager.current_time):
		ui_manager.show_victory_screen()
	
	ui_manager.queue_redraw()

func handle_movement_input():
	var move_vector = Vector2.ZERO
	
	if Input.is_action_just_pressed("move_up"):
		move_vector.y = -1
	elif Input.is_action_just_pressed("move_down"):
		move_vector.y = 1
	elif Input.is_action_just_pressed("move_left"):
		move_vector.x = -1
	elif Input.is_action_just_pressed("move_right"):
		move_vector.x = 1

	if move_vector != Vector2.ZERO:
		# Get hit quality for THIS exact time
		var hit_quality = rhythm_manager.get_hit_quality_for_time(rhythm_manager.current_time)

		if hit_quality > 0:  # Any quality above MISS
			player.try_move(move_vector, hit_quality)
		else:
			# Miss - show feedback and reset combos
			player.reset_combos()
			player.last_hit_quality = 0  # MISS
			player.hit_display_time = 2.0

func handle_menu_input():
	if ui_manager.is_victory_screen():
		handle_victory_menu_input()
	elif ui_manager.is_in_songs_menu():
		handle_songs_menu_input()
	else:
		handle_pause_menu_input()

func handle_pause_menu_input():
	# Keyboard navigation
	if Input.is_action_just_pressed("move_up"):
		var new_selection = (ui_manager.get_selected_button() - 1) % 3  # 3 menu buttons
		ui_manager.set_selected_button(new_selection)
	elif Input.is_action_just_pressed("move_down"):
		var new_selection = (ui_manager.get_selected_button() + 1) % 3
		ui_manager.set_selected_button(new_selection)
	elif Input.is_action_just_pressed("ui_accept"):  # Enter key
		execute_menu_action()
	
	# Mouse navigation
	var mouse_pos = get_global_mouse_position()
	var button_rects = ui_manager.get_button_rects()
	for i in range(button_rects.size()):
		if button_rects[i].has_point(mouse_pos):
			ui_manager.set_selected_button(i)
			if Input.is_action_just_pressed("ui_click"):
				execute_menu_action()

func handle_songs_menu_input():
	var available_songs = song_manager.get_available_songs()
	
	# Keyboard navigation
	if Input.is_action_just_pressed("move_up"):
		var new_selection = (ui_manager.get_selected_button() - 1) % available_songs.size()
		ui_manager.set_selected_button(new_selection)
	elif Input.is_action_just_pressed("move_down"):
		var new_selection = (ui_manager.get_selected_button() + 1) % available_songs.size()
		ui_manager.set_selected_button(new_selection)
	elif Input.is_action_just_pressed("ui_accept"):  # Enter key
		song_manager.load_song_by_index(ui_manager.get_selected_button())
		ui_manager.set_in_songs_menu(false)
		ui_manager.toggle_pause()  # Close pause menu after selecting song
	elif Input.is_action_just_pressed("ui_cancel"):  # Escape to go back
		ui_manager.set_in_songs_menu(false)
		ui_manager.set_selected_button(1)  # Back to "Songs" button
	
	# Mouse navigation
	var mouse_pos = get_global_mouse_position()
	var button_rects = ui_manager.get_button_rects()
	for i in range(button_rects.size()):
		if button_rects[i].has_point(mouse_pos):
			ui_manager.set_selected_button(i)
			if Input.is_action_just_pressed("ui_click"):
				song_manager.load_song_by_index(ui_manager.get_selected_button())
				ui_manager.set_in_songs_menu(false)
				ui_manager.toggle_pause()

func handle_victory_menu_input():
	# Keyboard navigation
	if Input.is_action_just_pressed("move_up"):
		var new_selection = (ui_manager.get_selected_button() - 1) % 3  # 3 victory buttons
		ui_manager.set_selected_button(new_selection)
	elif Input.is_action_just_pressed("move_down"):
		var new_selection = (ui_manager.get_selected_button() + 1) % 3
		ui_manager.set_selected_button(new_selection)
	elif Input.is_action_just_pressed("ui_accept"):  # Enter key
		execute_victory_action()

	# Mouse navigation
	var mouse_pos = get_global_mouse_position()
	var button_rects = ui_manager.get_button_rects()
	for i in range(button_rects.size()):
		if button_rects[i].has_point(mouse_pos):
			ui_manager.set_selected_button(i)
			if Input.is_action_just_pressed("ui_click"):
				execute_victory_action()

func execute_menu_action():
	match ui_manager.get_selected_button():
		0:  # Continue
			ui_manager.toggle_pause()
		1:  # Songs
			ui_manager.open_songs_menu()
		2:  # Quit
			get_tree().quit()

func execute_victory_action():
	match ui_manager.get_selected_button():
		0:  # Restart
			song_manager.load_song_by_index(song_manager.get_current_song_index())
			ui_manager.hide_victory_screen()
		1:  # New Song
			ui_manager.hide_victory_screen()
			ui_manager.toggle_pause()
			ui_manager.open_songs_menu()
		2:  # Exit
			get_tree().quit()

func _input(event):
	# Mouse click handling for pause menu
	if ui_manager.is_game_paused() and event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		var mouse_pos = event.position
		var button_rects = ui_manager.get_button_rects()
		for i in range(button_rects.size()):
			if button_rects[i].has_point(mouse_pos):
				ui_manager.set_selected_button(i)
				if ui_manager.is_in_songs_menu():
					song_manager.load_song_by_index(ui_manager.get_selected_button())
					ui_manager.set_in_songs_menu(false)
					ui_manager.toggle_pause()
				else:
					execute_menu_action()
				break
