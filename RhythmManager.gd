extends Node

# Rhythm system variables (time-based)
var bpm = 120
var beat_interval = 0.5  # Time between beats in seconds
var game_start_time = 0.0
var current_time = 0.0
var last_click_time = -1.0
var pause_start_time = 0.0
var total_pause_time = 0.0

# Hit quality timing windows (in seconds)
var exact_window = 0.015   # ±15ms for exact (30ms)
var perfect_window = 0.045  # ±45ms for perfect (90ms)
var excellent_window = 0.09  # ±90ms for excellent (180ms)
var good_window = 0.12      # ±120ms for good (240ms)

# Variable BPM system
var variable_beats = false
var bpm_changes = []  # Array of {start_time: float, end_time: float, bpm: int}

# Audio
var audio_player: AudioStreamPlayer  # For metronome
var music_player: AudioStreamPlayer  # For actual songs

func _ready():
	setup_audio()
	# Don't start timing until a song is loaded
	game_start_time = 0.0

func setup_audio():
	# Metronome audio player
	audio_player = AudioStreamPlayer.new()
	audio_player.name = "MetronomePlayer"
	add_child(audio_player)

	# Music audio player
	music_player = AudioStreamPlayer.new()
	music_player.name = "MusicPlayer"
	add_child(music_player)

	# Create a soft, subtle metronome sound
	create_soft_metronome_sound()

func create_soft_metronome_sound():
	# Create a soft sine wave click
	var sample_rate = 22050
	var duration = 0.08  # 80ms - shorter for subtlety
	var frequency = 1000  # 1kHz - clean, musical tone
	var samples = int(sample_rate * duration)

	# Create byte array directly for 16-bit audio
	var byte_data = PackedByteArray()

	for i in range(samples):
		var time = float(i) / sample_rate
		# Create a soft sine wave with exponential decay
		var envelope = exp(-time * 20.0)  # Quick fade out
		var sine_wave = sin(2.0 * PI * frequency * time)
		# Make it very soft and convert to 16-bit
		var sample_value = sine_wave * envelope * 0.1  # Very low volume
		var int_sample = int(sample_value * 16383.0)  # Half of 16-bit range for safety
		int_sample = clamp(int_sample, -16384, 16383)

		# Add as little-endian 16-bit
		byte_data.append(int_sample & 0xFF)
		byte_data.append((int_sample >> 8) & 0xFF)

	# Create AudioStreamWAV
	var stream = AudioStreamWAV.new()
	stream.format = AudioStreamWAV.FORMAT_16_BITS
	stream.mix_rate = sample_rate
	stream.stereo = false
	stream.data = byte_data

	audio_player.stream = stream
	audio_player.volume_db = -25  # Very quiet

func set_bpm(new_bpm):
	bpm = new_bpm
	beat_interval = 60.0 / new_bpm  # Time between beats in seconds

func get_current_time():
	if game_start_time == 0.0:
		return 0.0  # No song loaded yet
	return Time.get_unix_time_from_system() - game_start_time - total_pause_time

func get_hit_quality_for_time(time_seconds):
	var current_bpm = get_current_bpm_for_time(time_seconds)
	var current_beat_interval = 60.0 / current_bpm

	# Find the nearest beat
	var beat_number = round(time_seconds / current_beat_interval)
	var beat_time = beat_number * current_beat_interval
	var distance_from_beat = abs(time_seconds - beat_time)

	# Determine quality based on distance from beat
	if distance_from_beat <= exact_window:
		return 4  # EXACT
	elif distance_from_beat <= perfect_window:
		return 3  # PERFECT
	elif distance_from_beat <= excellent_window:
		return 2  # EXCELLENT
	elif distance_from_beat <= good_window:
		return 1  # GOOD
	else:
		return 0  # MISS

func should_play_metronome_at_time(time_seconds):
	var current_bpm = get_current_bpm_for_time(time_seconds)
	var current_beat_interval = 60.0 / current_bpm

	# Check if we're very close to a beat (within 1/60th of a second)
	var beat_number = round(time_seconds / current_beat_interval)
	var beat_time = beat_number * current_beat_interval
	var distance_from_beat = abs(time_seconds - beat_time)

	return distance_from_beat <= 0.016 and abs(time_seconds - last_click_time) > 0.1



func get_current_bpm_for_time(time_seconds):
	if variable_beats and bpm_changes.size() > 0:
		# Find which BPM section this time is in
		for bpm_change in bpm_changes:
			if time_seconds >= bpm_change.start_time and time_seconds <= bpm_change.end_time:
				return bpm_change.bpm
		return bpm  # Fallback
	else:
		return bpm



func play_metronome_click():
	if audio_player:
		audio_player.play()

func reset_time():
	game_start_time = Time.get_unix_time_from_system()
	total_pause_time = 0.0

func get_beat_progress_for_time(time_seconds):
	var current_bpm = get_current_bpm_for_time(time_seconds)
	var current_beat_interval = 60.0 / current_bpm
	var time_in_beat = fmod(time_seconds, current_beat_interval)
	return time_in_beat / current_beat_interval

func is_song_complete(time_seconds):
	if not variable_beats or bpm_changes.size() == 0:
		return false

	# Check if we've passed the end time of the last BPM change
	var last_change = bpm_changes[bpm_changes.size() - 1]
	return time_seconds > last_change.end_time

func load_song_audio(song_display_name):
	# Try to find matching audio file
	var audio_extensions = [".ogg", ".wav", ".mp3"]

	for ext in audio_extensions:
		var audio_path = "res://songs/" + song_display_name + ext
		if FileAccess.file_exists(audio_path):
			var audio_stream = load(audio_path)
			if audio_stream:
				music_player.stream = audio_stream
				print("Loaded audio: " + audio_path)
				return true

	print("No audio file found for: " + song_display_name)
	music_player.stream = null
	return false

func play_song_audio():
	if music_player.stream:
		music_player.play()
		print("Started playing song audio")

func stop_song_audio():
	if music_player.is_playing():
		music_player.stop()

func set_music_volume(volume_db):
	music_player.volume_db = volume_db

func set_metronome_volume(volume_db):
	audio_player.volume_db = volume_db

func pause_music():
	if music_player.is_playing():
		music_player.stream_paused = true
		pause_start_time = Time.get_unix_time_from_system()

func resume_music():
	if music_player.stream:
		music_player.stream_paused = false
		# Add the pause duration to total pause time
		if pause_start_time > 0:
			total_pause_time += Time.get_unix_time_from_system() - pause_start_time
			pause_start_time = 0.0
