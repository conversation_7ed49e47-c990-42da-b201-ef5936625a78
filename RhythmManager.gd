extends Node

# Rhythm system variables
var bpm = 120
var frames_per_beat = 60
var game_start_frame = 0
var current_frame = 0
var last_click_frame = -1

# Hit quality lookup table (pre-calculated)
var frame_quality = {}     # frame_number: quality_value (0-4)

# Variable BPM system
var variable_beats = false
var bpm_changes = []  # Array of {start_frame: int, end_frame: int, bpm: int}

# Audio
var audio_player: AudioStreamPlayer

func _ready():
	setup_audio()

func setup_audio():
	audio_player = AudioStreamPlayer.new()
	add_child(audio_player)

	# Create a soft, subtle metronome sound
	create_soft_metronome_sound()

func create_soft_metronome_sound():
	# Create a soft sine wave click
	var sample_rate = 22050
	var duration = 0.1  # 100ms
	var frequency = 800  # Higher pitch, softer tone
	var samples = int(sample_rate * duration)

	var audio_data = PackedFloat32Array()
	audio_data.resize(samples)

	for i in range(samples):
		var time = float(i) / sample_rate
		# Create a soft sine wave with exponential decay
		var envelope = exp(-time * 15.0)  # Quick fade out
		var sine_wave = sin(2.0 * PI * frequency * time)
		# Make it much softer (lower volume)
		audio_data[i] = sine_wave * envelope * 0.15  # Very low volume

	# Create AudioStreamWAV
	var stream = AudioStreamWAV.new()
	stream.format = AudioStreamWAV.FORMAT_32_FLOAT
	stream.mix_rate = sample_rate
	stream.data = audio_data.to_byte_array()

	audio_player.stream = stream
	audio_player.volume_db = -20  # Additional volume reduction

func set_bpm(new_bpm):
	bpm = new_bpm
	frames_per_beat = int(120.0 / (new_bpm / 60.0))  # 120fps assumption
	generate_hit_quality_tables()

func generate_hit_quality_tables():
	# Clear existing table
	frame_quality.clear()
	
	if variable_beats and bpm_changes.size() > 0:
		# Generate based on variable BPM changes
		generate_variable_beat_mapping()
	else:
		# Generate standard mapping for constant BPM
		generate_standard_beat_mapping()

func generate_standard_beat_mapping():
	# Generate for next 100000 frames (about 14 minutes at 120fps)
	for frame in range(100000):
		var frame_in_beat = frame % frames_per_beat
		var distance_from_beat_start = min(frame_in_beat, frames_per_beat - frame_in_beat)
		
		# Define frame windows with numerical values
		# 0=MISS, 1=GOOD, 2=EXCELLENT, 3=PERFECT, 4=EXACT
		if distance_from_beat_start == 0:
			frame_quality[frame] = 4  # EXACT
		elif distance_from_beat_start <= 5:  # ±5 frames for perfect
			frame_quality[frame] = 3  # PERFECT
		elif distance_from_beat_start <= 12: # ±12 frames for excellent  
			frame_quality[frame] = 2  # EXCELLENT
		elif distance_from_beat_start <= 24: # ±24 frames for good
			frame_quality[frame] = 1  # GOOD
		else:
			frame_quality[frame] = 0  # MISS

func generate_variable_beat_mapping():
	# Generate mapping based on BPM changes
	for bpm_change in bpm_changes:
		var start_frame = bpm_change.start_frame
		var end_frame = bpm_change.end_frame
		var section_bpm = bpm_change.bpm
		var section_frames_per_beat = int(120.0 / (section_bpm / 60.0))
		
		for frame in range(start_frame, end_frame + 1):
			var frame_in_beat = frame % section_frames_per_beat
			var distance_from_beat_start = min(frame_in_beat, section_frames_per_beat - frame_in_beat)
			
			# Define frame windows with numerical values
			if distance_from_beat_start == 0:
				frame_quality[frame] = 4  # EXACT
			elif distance_from_beat_start <= 5:  # ±5 frames for perfect
				frame_quality[frame] = 3  # PERFECT
			elif distance_from_beat_start <= 12: # ±12 frames for excellent  
				frame_quality[frame] = 2  # EXCELLENT
			elif distance_from_beat_start <= 24: # ±24 frames for good
				frame_quality[frame] = 1  # GOOD
			else:
				frame_quality[frame] = 0  # MISS

func get_current_frame():
	return Engine.get_process_frames() - game_start_frame

func get_hit_quality_for_frame(frame_number):
	if frame_number in frame_quality:
		return frame_quality[frame_number]
	else:
		return 0  # MISS if not found

func should_play_metronome_at_frame(frame_number):
	if variable_beats and bpm_changes.size() > 0:
		# Find which BPM section this frame is in
		for bpm_change in bpm_changes:
			if frame_number >= bpm_change.start_frame and frame_number <= bpm_change.end_frame:
				var section_bpm = bpm_change.bpm
				var section_frames_per_beat = int(120.0 / (section_bpm / 60.0))
				var frame_in_beat = frame_number % section_frames_per_beat
				return frame_in_beat == 0
		return false
	else:
		# Standard constant BPM
		var frame_in_beat = frame_number % frames_per_beat
		return frame_in_beat == 0

func get_frames_per_beat_for_frame(frame_number):
	if variable_beats and bpm_changes.size() > 0:
		# Find which BPM section this frame is in
		for bpm_change in bpm_changes:
			if frame_number >= bpm_change.start_frame and frame_number <= bpm_change.end_frame:
				var section_bpm = bpm_change.bpm
				return int(120.0 / (section_bpm / 60.0))
		return frames_per_beat  # Fallback
	else:
		return frames_per_beat

func get_current_bpm_for_frame(frame_number):
	if variable_beats and bpm_changes.size() > 0:
		# Find which BPM section this frame is in
		for bpm_change in bpm_changes:
			if frame_number >= bpm_change.start_frame and frame_number <= bpm_change.end_frame:
				return bpm_change.bpm
		return bpm  # Fallback
	else:
		return bpm

func get_beat_progress_for_frame(frame_number):
	var current_frames_per_beat = get_frames_per_beat_for_frame(frame_number)
	var frame_in_beat = frame_number % current_frames_per_beat
	return float(frame_in_beat) / float(current_frames_per_beat)

func play_metronome_click():
	if audio_player:
		audio_player.play()

func reset_frame_count():
	game_start_frame = Engine.get_process_frames()
