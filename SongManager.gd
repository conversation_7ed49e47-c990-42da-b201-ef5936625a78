extends Node

# Songs system
var available_songs = []
var current_song_index = 0

# References to other managers
var rhythm_manager: Node
var player: Node

func _ready():
	# Get references to other managers
	rhythm_manager = get_node("../RhythmManager")
	player = get_node("../Player")

	# Load available songs
	load_songs()

	# Initialize with default song (use call_deferred to ensure all nodes are ready)
	call_deferred("load_song_by_index", 0)

func load_songs():
	available_songs.clear()
	var song_data = []
	
	var dir = DirAccess.open("res://songs/")
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			if file_name.ends_with(".txt"):
				var file_path = "res://songs/" + file_name
				var file = FileAccess.open(file_path, FileAccess.READ)
				if file:
					var first_line = file.get_line()
					file.close()
					
					# Parse display name from first line (e.g., "1. 120 BPM")
					var display_name = first_line.strip_edges()
					var file_base = file_name.get_basename()
					
					# Extract ID number for sorting
					var id = 999  # Default high number if no ID found
					if display_name.begins_with(str(display_name.split(".")[0])):
						id = int(display_name.split(".")[0])
					
					song_data.append({"id": id, "display_name": display_name, "file_name": file_base})
			file_name = dir.get_next()
		dir.list_dir_end()
	
	# Sort by ID number
	song_data.sort_custom(func(a, b): return a.id < b.id)
	
	# Extract display names in sorted order
	for song in song_data:
		available_songs.append(song.display_name)

func load_song_by_index(index):
	if index >= 0 and index < available_songs.size():
		current_song_index = index
		var song_name = available_songs[index]
		load_song(song_name)

func load_song(song_display_name):
	# Find the corresponding file for this display name
	var dir = DirAccess.open("res://songs/")
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			if file_name.ends_with(".txt"):
				var file_path = "res://songs/" + file_name
				var file = FileAccess.open(file_path, FileAccess.READ)
				if file:
					var first_line = file.get_line()
					if first_line.strip_edges() == song_display_name:
						# Found the right file, parse the rest
						var content = file.get_as_text()
						file.close()
						
						# Reset variable beats system
						rhythm_manager.variable_beats = false
						rhythm_manager.bpm_changes.clear()
						
						# Parse song file
						var default_bpm = 120
						var lines = content.split("\n")
						for line in lines:
							line = line.strip_edges()
							if line.begins_with("bpm="):
								default_bpm = int(line.split("=")[1])
							elif line.begins_with("variableBeats="):
								rhythm_manager.variable_beats = line.split("=")[1].to_lower() == "true"
							elif line.begins_with("bpm_change:"):
								# Format: bpm_change:start_time:end_time:bpm
								var parts = line.split(":")
								if parts.size() == 4:
									var change = {
										"start_time": float(parts[1]),
										"end_time": float(parts[2]),
										"bpm": int(parts[3])
									}
									rhythm_manager.bpm_changes.append(change)
						
						# Only set BPM if not using variable beats
						if not rhythm_manager.variable_beats:
							rhythm_manager.set_bpm(default_bpm)
						else:
							rhythm_manager.bpm = default_bpm  # Store for display purposes only
							rhythm_manager.generate_hit_quality_tables()  # Generate variable beat mapping
						
						# Load audio file if it exists
						rhythm_manager.stop_song_audio()  # Stop any currently playing audio
						rhythm_manager.load_song_audio(song_display_name)

						# Reset player position and frame count when loading new song
						if player:
							player.reset_position()
							player.reset_combos()
						if rhythm_manager:
							rhythm_manager.reset_time()

						# Start playing the song audio
						rhythm_manager.play_song_audio()

						print("Loaded song: " + song_display_name + " (BPM: " + str(rhythm_manager.bpm) + ")")
						
						dir.list_dir_end()
						break
					file.close()
			file_name = dir.get_next()
		dir.list_dir_end()

func get_available_songs():
	return available_songs

func get_current_song_index():
	return current_song_index
