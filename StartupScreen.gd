extends Control

# Startup menu
var menu_buttons = ["Choose Song", "Close Game"]
var selected_button = 0
var button_rects = []

func _ready():
	# Fill the screen
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)

func _process(_delta):
	handle_input()
	queue_redraw()

func handle_input():
	# Keyboard navigation
	if Input.is_action_just_pressed("move_up"):
		selected_button = (selected_button - 1) % menu_buttons.size()
	elif Input.is_action_just_pressed("move_down"):
		selected_button = (selected_button + 1) % menu_buttons.size()
	elif Input.is_action_just_pressed("ui_accept"):  # Enter key
		execute_action()
	
	# Mouse navigation
	if get_viewport():  # Check if viewport exists
		var mouse_pos = get_global_mouse_position()
		for i in range(button_rects.size()):
			if button_rects[i].has_point(mouse_pos):
				selected_button = i
				if Input.is_action_just_pressed("ui_cut"):
					execute_action()

func execute_action():
	match selected_button:
		0:  # Choose Song
			# Load the main game scene
			get_tree().change_scene_to_file("res://MainModular.tscn")
		1:  # Close Game
			get_tree().quit()

func _input(event):
	# Mouse click handling
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		var mouse_pos = event.position
		for i in range(button_rects.size()):
			if button_rects[i].has_point(mouse_pos):
				selected_button = i
				execute_action()
				break

func _draw():
	var font = ThemeDB.fallback_font
	var viewport_size = get_viewport().get_visible_rect().size
	
	# Background
	draw_rect(Rect2(Vector2.ZERO, viewport_size), Color(0.1, 0.1, 0.2, 1.0))
	
	# Title
	var title_text = "RHYTHM ROOTS"
	var title_size = font.get_string_size(title_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 64)
	var title_pos = Vector2(viewport_size.x / 2 - title_size.x / 2, viewport_size.y / 3)
	draw_string(font, title_pos, title_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 64, Color.WHITE)
	
	# Menu buttons
	button_rects.clear()
	var button_start_y = viewport_size.y / 2
	var button_width = 300
	var button_height = 60
	var button_spacing = 20
	
	for i in range(menu_buttons.size()):
		var button_y = button_start_y + i * (button_height + button_spacing)
		var button_x = viewport_size.x / 2 - button_width / 2
		var button_rect = Rect2(button_x, button_y, button_width, button_height)
		button_rects.append(button_rect)
		
		# Button colors (same blue-purple theme)
		var button_color = Color(0.4, 0.5, 0.9, 0.8)  # Slightly more blue
		var highlight_color = Color(0.6, 0.8, 1.0, 0.9)  # Baby blue
		
		if i == selected_button:
			draw_rect(button_rect, highlight_color)
		else:
			draw_rect(button_rect, button_color)
		
		# Button text
		var button_text = menu_buttons[i]
		var text_size = font.get_string_size(button_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 32)
		var text_pos = Vector2(
			button_rect.position.x + button_rect.size.x / 2 - text_size.x / 2,
			button_rect.position.y + button_rect.size.y / 2 + text_size.y / 2
		)
		draw_string(font, text_pos, button_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 32, Color.WHITE)
	
	# Instructions
	var instruction_text = "Arrow Keys: Navigate    Enter: Select    Mouse: Click"
	var instruction_size = font.get_string_size(instruction_text, HORIZONTAL_ALIGNMENT_CENTER, -1, 18)
	var instruction_pos = Vector2(viewport_size.x / 2 - instruction_size.x / 2, viewport_size.y - 50)
	draw_string(font, instruction_pos, instruction_text, HORIZONTAL_ALIGNMENT_LEFT, -1, 18, Color.LIGHT_GRAY)
